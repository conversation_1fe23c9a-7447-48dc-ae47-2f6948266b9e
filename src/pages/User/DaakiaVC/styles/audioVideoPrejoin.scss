.video-prejoin-container {
  position: relative;
  padding: 0 !important;
  margin: 0 auto !important;
  background-color: #1a1a1a;
  border-radius: 16px;
  overflow: hidden;
  width: 100%;
  max-width: 380px;
  height: auto;
  aspect-ratio: 16/9;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: 992px) {
    max-width: 340px;
  }

  @media (max-width: 768px) {
    width: 90%;
    max-width: 300px;
    aspect-ratio: 16/9;
    margin-top: 0 !important;
  }

  @media (max-width: 480px) {
    width: 92%;
    max-width: 260px;
    aspect-ratio: 16/9;
    margin-top: 0 !important;
  }

  // For medium screens and up (side-by-side layout)
  @media (min-width: 768px) {
    .col-md-6:first-child & {
      max-width: none;
      width: 100%;
      height: 100%;
      aspect-ratio: auto;
    }
  }

  .avatar-container {
    transition: all 0.3s ease;
    width: 120px !important;
    height: 120px !important;
    font-size: 46px !important;
    padding: 16px !important;
    border-radius: 50%;
    background: #7E47EB;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;

    @media (max-width: 768px) {
      width: 100px !important;
      height: 100px !important;
      font-size: 38px !important;
      padding: 14px !important;
    }

    @media (max-width: 480px) {
      width: 80px !important;
      height: 80px !important;
      font-size: 30px !important;
      padding: 12px !important;
    }
  }

  .avatar-icon {
    font-size: 46px !important;

    @media (max-width: 768px) {
      font-size: 38px !important;
    }

    @media (max-width: 480px) {
      font-size: 30px !important;
    }
  }

  .control-bar-container {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(67, 67, 67, 0.7);
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    border-radius: 12px;
    padding: 0.4rem;
    display: flex;
    gap: 0.35rem;
    justify-content: center;
    align-items: center;
    width: auto;
    min-width: min-content;
    max-width: 100%;
    flex-wrap: nowrap;
    box-sizing: border-box;
    transition: all 0.3s ease;

    @media (max-width: 768px) {
      padding: 0.5rem !important;
      gap: 0.4rem !important;
      bottom: 1rem !important;
    }

    @media (max-width: 480px) {
      padding: 0.45rem !important;
      gap: 0.35rem !important;
      bottom: 0.75rem !important;
    }
  }

  .lk-button-group.audio,
  .lk-button-group.video {
    position: relative;
    width: auto !important;
    display: flex;
    align-items: center;
    gap: 0.15rem;
  }

  .control-button {
    width: 3.2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 7px;
    transition: all 0.2s ease;
    flex-shrink: 0;
    position: relative;
    background-color: transparent;

    &:hover,
    &:focus {
      background-color: rgba(255, 255, 255, 0.15) !important;
    }

    &.permission-warning {
      border: 2px solid #E1C640;
    }

    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;

      &:hover,
      &:focus {
        background-color: transparent !important;
      }

      .control-icon {
        opacity: 0.6;
      }
    }

    .warning-icon {
      position: absolute;
      top: -5px;
      right: -5px;
      width: 14px;
      height: 14px;
    }

    .control-icon {
      font-size: 1.2rem;
      color: white;
      width: 1.2rem;
      height: 1.2rem;
      transition: all 0.3s ease;

      &.virtual-bg-icon {
        width: 1.5rem;
        height: 1.5rem;
      }

      @media (max-width: 768px) {
        font-size: 1.3rem !important;

        &.virtual-bg-icon {
          width: 1.6rem;
          height: 1.6rem;
        }
      }

      @media (max-width: 480px) {
        font-size: 1.2rem !important;

        &.virtual-bg-icon {
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }

    @media (max-width: 768px) {
      width: 2.5rem !important;
      height: 2.5rem !important;
    }

    @media (max-width: 480px) {
      width: 2.2rem !important;
      height: 2.2rem !important;
    }
  }

  .dropdown-button {
    width: 1rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 10px;
    transition: all 0.2s ease;
    background-color: transparent;
    margin-left: -0.2rem;

    .dropdown-icon {
      font-size: 0.6rem;
      color: white;
    }

    &:hover,
    &:focus {
      background-color: rgba(255, 255, 255, 0.15) !important;
    }

    @media (max-width: 768px) {
      width: 1.2rem !important;
      height: 2.5rem !important;
      margin-left: -0.15rem;

      .dropdown-icon {
        font-size: 0.7rem;
      }
    }

    @media (max-width: 480px) {
      width: 1.1rem !important;
      height: 2.2rem !important;
      margin-left: -0.1rem;

      .dropdown-icon {
        font-size: 0.65rem;
      }
    }
  }

  .mute-button {
    position: absolute;
    top: 0.9375rem;
    right: 0.9375rem;
    width: 2.2rem;
    height: 2.2rem;
    border-radius: 20px;
    background-color: rgba(67, 67, 67, 0.7);
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    padding: 0.5rem;
    transition: all 0.2s ease;

    &:hover,
    &:focus {
      transform: scale(1.05);
      background-color: rgba(102, 102, 102, 0.7);
    }

    @media (max-width: 768px) {
      width: 2.2rem !important;
      height: 2.2rem !important;
      padding: 0.5rem !important;
      top: 0.75rem !important;
      right: 0.75rem !important;

      .control-icon {
        font-size: 1.1rem !important;
      }
    }

    @media (max-width: 480px) {
      width: 2rem !important;
      height: 2rem !important;
      padding: 0.45rem !important;
      top: 0.6rem !important;
      right: 0.6rem !important;

      .control-icon {
        font-size: 1rem !important;
      }
    }
  }

  .lk-video-container {
    background-color: #1a1a1a;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    aspect-ratio: 16/9;

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    @media (max-width: 768px) {
      aspect-ratio: 16/9;
    }

    @media (max-width: 480px) {
      aspect-ratio: 16/9;
    }

    // For medium screens and up (side-by-side layout)
    @media (min-width: 768px) {
      .col-md-6:first-child & {
        aspect-ratio: auto;
      }
    }
  }

  .lk-camera-off-note {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #292929;
    border-radius: 16px;
    aspect-ratio: 16/9;

    // For medium screens and up (side-by-side layout)
    @media (min-width: 768px) {
      .col-md-6:first-child & {
        aspect-ratio: auto;
      }
    }
  }
}
